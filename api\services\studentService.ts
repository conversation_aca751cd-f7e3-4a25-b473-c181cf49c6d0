/**
 * Student Service
 * 
 * Handles all student-related API calls:
 * - CRUD operations
 * - Filtering and pagination
 * - Statistics and reports
 * - Bulk operations
 */

import { api } from '../apiClient';
import type { 
  Student, 
  StudentFilters, 
  StudentStats,
  StudentCreate,
  StudentUpdate,
  StudentListResponse,
  StudentToggleResponse,
  StudentPhotoResponse,
  StudentImportResponse
} from '../../types';

export interface StudentQuery extends StudentFilters {
  page?: number;
  size?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class StudentService {
  private static readonly BASE_URL = '/students';

  /**
   * Get all students with filtering and pagination
   */
  static async getStudents(query: StudentQuery = {}): Promise<StudentListResponse> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await api.get<StudentListResponse>(url);
    return response.data;
  }

  /**
   * Get student by ID
   */
  static async getStudent(id: string): Promise<Student> {
    const response = await api.get<Student>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Create new student
   */
  static async createStudent(data: StudentCreate): Promise<Student> {
    const response = await api.post<Student>(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Update student
   */
  static async updateStudent(id: string, data: StudentUpdate): Promise<Student> {
    const response = await api.put<Student>(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Toggle student active status
   */
  static async toggleStudent(id: string): Promise<StudentToggleResponse> {
    const response = await api.post<StudentToggleResponse>(`${this.BASE_URL}/${id}/toggle-active`);
    return response.data;
  }

  /**
   * Delete student (soft delete)
   */
  static async deleteStudent(id: string): Promise<void> {
    await api.delete<void>(`${this.BASE_URL}/${id}`);
  }

  /**
   * Upload student photo
   */
  static async uploadStudentPhoto(id: string, file: File): Promise<StudentPhotoResponse> {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post<StudentPhotoResponse>(`${this.BASE_URL}/${id}/photo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Import students from CSV
   */
  static async importStudents(file: File): Promise<StudentImportResponse> {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post<StudentImportResponse>(`${this.BASE_URL}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Get student statistics
   */
  static async getStudentStats(): Promise<StudentStats> {
    const response = await api.get<StudentStats>(`${this.BASE_URL}/stats`);
    return response.data;
  }

  /**
   * Get students by grade
   */
  static async getStudentsByGrade(grade: string): Promise<Student[]> {
    const response = await api.get<Student[]>(`${this.BASE_URL}/grade/${grade}`);
    return response.data;
  }

  /**
   * Get students by class
   */
  static async getStudentsByClass(className: string): Promise<Student[]> {
    const response = await api.get<Student[]>(`${this.BASE_URL}/class/${className}`);
    return response.data;
  }

  /**
   * Search students
   */
  static async searchStudents(query: string): Promise<Student[]> {
    const response = await api.get<Student[]>(`${this.BASE_URL}/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  /**
   * Get available grades
   */
  static async getGrades(): Promise<string[]> {
    const response = await api.get<string[]>(`${this.BASE_URL}/grades`);
    return response.data;
  }

  /**
   * Get available classes
   */
  static async getClasses(): Promise<string[]> {
    const response = await api.get<string[]>(`${this.BASE_URL}/classes`);
    return response.data;
  }

  /**
   * Export students data
   */
  static async exportStudents(
    format: 'csv' | 'excel' | 'pdf',
    filters?: StudentFilters
  ): Promise<void> {
    const params = new URLSearchParams({ format });
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });
    }

    // Note: download method needs to be implemented in apiClient
    await api.get(`${this.BASE_URL}/export?${params.toString()}`);
  }

  /**
   * Get student's grades
   */
  static async getStudentGrades(studentId: string): Promise<Array<{
    id: string;
    subject: string;
    exam: string;
    marksObtained: number;
    totalMarks: number;
    percentage: number;
    grade: string;
    date: string;
  }>> {
    const response = await api.get<Array<{
      id: string;
      subject: string;
      exam: string;
      marksObtained: number;
      totalMarks: number;
      percentage: number;
      grade: string;
      date: string;
    }>>(`${this.BASE_URL}/${studentId}/grades`);
    return response.data;
  }

  /**
   * Get student's attendance
   */
  static async getStudentAttendance(
    studentId: string, 
    dateFrom?: string, 
    dateTo?: string
  ): Promise<Array<{
    id: string;
    date: string;
    status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
    checkInTime?: string;
    checkOutTime?: string;
    notes?: string;
  }>> {
    const params = new URLSearchParams();
    if (dateFrom) params.append('dateFrom', dateFrom);
    if (dateTo) params.append('dateTo', dateTo);

    const url = `${this.BASE_URL}/${studentId}/attendance${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await api.get<Array<{
      id: string;
      date: string;
      status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
      checkInTime?: string;
      checkOutTime?: string;
      notes?: string;
    }>>(url);
    return response.data;
  }

  /**
   * Get student's fee records
   */
  static async getStudentFees(studentId: string): Promise<Array<{
    id: string;
    feeType: string;
    amount: number;
    dueDate: string;
    paidDate?: string;
    status: 'PAID' | 'PENDING' | 'OVERDUE';
    paymentMethod?: string;
  }>> {
    const response = await api.get<Array<{
      id: string;
      feeType: string;
      amount: number;
      dueDate: string;
      paidDate?: string;
      status: 'PAID' | 'PENDING' | 'OVERDUE';
      paymentMethod?: string;
    }>>(`${this.BASE_URL}/${studentId}/fees`);
    return response.data;
  }

  /**
   * Transfer student to another class
   */
  static async transferStudent(
    studentId: string, 
    newGrade: string, 
    newClass: string,
    transferDate: string,
    reason?: string
  ): Promise<Student> {
    const response = await api.post<Student>(`${this.BASE_URL}/${studentId}/transfer`, {
      newGrade,
      newClass,
      transferDate,
      reason,
    });
    return response.data;
  }

  /**
   * Graduate student
   */
  static async graduateStudent(
    studentId: string,
    graduationDate: string,
    finalGrade?: string
  ): Promise<Student> {
    const response = await api.post<Student>(`${this.BASE_URL}/${studentId}/graduate`, {
      graduationDate,
      finalGrade,
    });
    return response.data;
  }
}

// Export default instance
export const studentService = StudentService;
